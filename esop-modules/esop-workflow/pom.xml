<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zjwly</groupId>
        <artifactId>esop-modules</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>esop-workflow</artifactId>

    <description>
        工作流模块
    </description>

    <dependencies>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-sse</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-idempotent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-translation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.warm</groupId>
            <artifactId>warm-flow-mybatis-plus-sb3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.warm</groupId>
            <artifactId>warm-flow-plugin-ui-sb-web</artifactId>
        </dependency>
    </dependencies>

</project>

