<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zjwly</groupId>
        <artifactId>esop-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>esop-sm-service</artifactId>
    <description>电站监控系统</description>

    <properties>
        <taos.version>3.5.3</taos.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-satoken</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
        </dependency>

        <!-- 表达式引擎 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
        </dependency>
    </dependencies>
</project>
