<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.SmAlarmInfoMapper">
    <select id="listAlarmInfo" resultType="com.zjwly.sm.domain.vo.AlarmInfoVo">
        select d.device_name, d.device_code, ai.point_code, ai.start_time, alc.`name` as alarm_name, ai.end_time, ai.duration from sm_alarm_info ai left join sm_device d on ai.device_code = d.device_code left join sm_alarm_level_config alc on ai.device_code = alc.device_code and ai.point_code = alc.point_code
        <where>
            <if test="occurStartTime != null and occurStartTime != ''">
                and ai.start_time >= #{occurStartTime}
            </if>
            <if test="occurEndTime != null and occurEndTime != ''">
                and ai.start_time &lt;= #{occurEndTime}
            </if>
            <if test="overStartTime != null and overStartTime != ''">
                and ai.end_time >= #{overStartTime}
            </if>
            <if test="overEndTime != null and overEndTime != ''">
                and ai.end_time &lt;= #{overEndTime}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and d.device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="alarmName != null and alarmName != ''">
                and alc.`name` like concat('%', #{alarmName}, '%')
            </if>
        </where>
    </select>

    <select id="lastAlarmInfo" resultType="string">
        select concat(d.device_name, '-', alc.`name`)
        from sm_alarm_info ai
                 left join sm_device d on ai.device_code = d.device_code
                 left join sm_alarm_level_config alc
                           on ai.device_code = alc.device_code and ai.point_code = alc.point_code
        where ai.end_time is null
        order by ai.id desc
        limit ${limit}
    </select>

    <select id="listRunningStatusAlarm" resultType="com.zjwly.sm.domain.vo.AlarmInfoVo">
        select ai.point_code,
               ai.start_time,
               alc.`name` as alarm_name,
               ai.end_time,
               ai.duration
        from sm_alarm_info ai
                 left join sm_alarm_level_config alc
                           on ai.device_code = alc.device_code and ai.point_code = alc.point_code
        where ai.end_time is null
    </select>
</mapper>
