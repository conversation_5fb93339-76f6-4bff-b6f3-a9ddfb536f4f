<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.SmDeviceMapper">
    <select id="listDeviceByPCode" resultType="com.zjwly.sm.domain.SmDevice">
        select child.*
        from sm_device as child
                 join
             sm_device as parent on child.device_pid = parent.device_id
        where parent.device_code = #{pCode}
    </select>
</mapper>
