<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.SmWiringDiagramConfigMapper">
  <resultMap id="BaseResultMap" type="com.zjwly.sm.domain.SmWiringDiagramConfig">
    <!--@mbg.generated-->
    <!--@Table sm_wiring_diagram_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="point_code" jdbcType="VARCHAR" property="pointCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_code, point_code, `type`
  </sql>
</mapper>