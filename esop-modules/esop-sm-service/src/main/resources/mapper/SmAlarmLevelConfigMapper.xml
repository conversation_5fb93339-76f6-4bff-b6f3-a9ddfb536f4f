<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.SmAlarmLevelConfigMapper">
  <resultMap id="BaseResultMap" type="com.zjwly.sm.domain.SmAlarmLevelConfig">
    <!--@mbg.generated-->
    <!--@Table sm_alarm_level_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="point_code" jdbcType="VARCHAR" property="pointCode" />
    <result column="level" jdbcType="INTEGER" property="level" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, device_code, point_code, `level`
  </sql>
</mapper>