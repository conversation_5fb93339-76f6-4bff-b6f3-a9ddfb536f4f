<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.SmHomepageStationConfigMapper">
  <resultMap id="BaseResultMap" type="com.zjwly.sm.domain.SmHomepageStationConfig">
    <!--@mbg.generated-->
    <!--@Table sm_homepage_station_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="battery_type" jdbcType="VARCHAR" property="batteryType" />
    <result column="connection_voltage" jdbcType="VARCHAR" property="connectionVoltage" />
    <result column="connection_point" jdbcType="VARCHAR" property="connectionPoint" />
    <result column="soc_code" jdbcType="VARCHAR" property="socCode" />
    <result column="active_power_code" jdbcType="VARCHAR" property="activePowerCode" />
    <result column="reactive_power_code" jdbcType="VARCHAR" property="reactivePowerCode" />
    <result column="stored_energy" jdbcType="VARCHAR" property="storedEnergyCode" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, station_name, battery_type, connection_voltage, connection_point, soc_code, active_power_code,
    reactive_power_code, stored_energy, start_date
  </sql>
</mapper>
