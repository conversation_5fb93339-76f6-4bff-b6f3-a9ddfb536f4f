<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.TsdbMapper">
    <select id="lastTelemetryList" resultType="com.zjwly.sm.domain.vo.TelemetryVo">
        select point_code, last(val) as val
        from esop.history_${location}
        where ts &gt;= #{startTime}
          and ts &lt;= #{endTime}
        group by point_code
    </select>
    <select id="lastTelesignalList" resultType="com.zjwly.sm.domain.vo.TelesignalVo">
        select point_code, last(val) as val
        from esop.event_${location}
        where ts &gt;= #{startTime}
          and ts &lt;= #{endTime}
        group by point_code
    </select>

    <select id="listTelemetryCurve" resultType="com.zjwly.sm.domain.vo.TelemetryCurveVo">
        select _wstart as ts, first(val) as val
        from esop.history_${location}
        where ts &gt;= #{startTime}
          and ts &lt;= #{endTime}
          and point_code = #{pointCode} interval
            (${interval})
            fill(NULL)
    </select>
    <select id="listTelesignalCurve" resultType="com.zjwly.sm.domain.vo.TelesignalCurveVo">
        select _wstart AS ts, first(val) as val
        from esop.event_${location}
        where ts &gt;= #{startTime}
          and ts &lt;= #{endTime}
          and point_code = #{pointCode} interval
            (${interval})
            fill(NULL)
    </select>

    <select id="lastTelemetry" resultType="java.math.BigDecimal">
        select val
        from esop.history_${location}
        where point_code = #{pointCode} and ts &gt;= #{startTime} and ts &lt;= #{endTime}
        order by ts desc limit 1
    </select>

    <select id="lastTelesignal" resultType="java.lang.Integer">
        select val
        from esop.event_${location}
        where point_code = #{pointCode} and ts &gt;= #{startTime} and ts &lt;= #{endTime}
        order by ts desc limit 1
    </select>

    <select id="listTelemetryPoint" resultType="com.zjwly.sm.domain.vo.TelemetryVo">
        select point_code, last(val) as val
        from esop.history_${location}
        <where>
            <if test="startTime != null and startTime != ''">
                ts &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and ts &lt;= #{endTime}
            </if>
            and point_code in (
            <foreach collection="pointCodes" item="item" separator=",">
                #{item}
            </foreach>
            )
        </where>
        group by point_code
    </select>

    <select id="listAvgTelemetryCurve" resultType="com.zjwly.sm.domain.vo.TelemetryCurveVo">
        select _wstart as ts, avg(val) as val
        from esop.history_${location}
        where ts &gt;= #{startTime}
          and ts &lt; #{endTime}
          and point_code = #{pointCode} interval(${interval}) fill(NULL);
    </select>

    <select id="listTelesignalPoint" resultType="com.zjwly.sm.domain.vo.TelesignalVo">
        select point_code, last(val) as val
        from esop.event_${location}
        <where>
            <if test="startTime != null and startTime != ''">
                ts &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and ts &lt;= #{endTime}
            </if>
            and point_code in (
            <foreach collection="pointCodes" item="item" separator=",">
                #{item}
            </foreach>
            )
        </where>
        group by point_code
    </select>

    <select id="lastTelesignalByTimeRange" resultType="java.lang.Integer">
        select last(val) as val
        from esop.event_${location}
        <where>
            <if test="startTime != null and startTime != ''">
                ts &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and ts &lt;= #{endTime}
            </if>
            and point_code = #{pointCode}
        </where>
    </select>
</mapper>
