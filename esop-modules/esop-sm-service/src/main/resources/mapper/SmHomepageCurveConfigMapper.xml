<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.sm.mapper.SmHomepageCurveConfigMapper">
  <resultMap id="BaseResultMap" type="com.zjwly.sm.domain.SmHomepageCurveConfig">
    <!--@mbg.generated-->
    <!--@Table sm_homepage_curve_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="curve_name" jdbcType="VARCHAR" property="curveName" />
    <result column="curve_type" jdbcType="OTHER" property="curveType" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="point_code" jdbcType="VARCHAR" property="pointCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, curve_name, curve_type, device_code, device_name, point_code, unit
  </sql>
</mapper>