package com.zjwly.sm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjwly.common.core.domain.R;
import com.zjwly.sm.domain.SmDevice;
import com.zjwly.sm.domain.bo.SectionBo;
import com.zjwly.sm.domain.vo.CascaderVo;
import com.zjwly.sm.domain.vo.DataMonitorVo;
import com.zjwly.sm.domain.vo.SectionVo;
import com.zjwly.sm.domain.vo.WiringDiagramDataVo;
import com.zjwly.sm.enums.SmDeviceEnum;
import com.zjwly.sm.enums.SmStatusEnum;
import com.zjwly.sm.service.DataMonitorService;
import com.zjwly.sm.service.SmDeviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据监控
 *
 * <AUTHOR>
 * @date 2025/04/05 18:18
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ds/sm/dataMonitor")
public class DataMonitorController {

    private final DataMonitorService dataMonitorService;
    private final SmDeviceService smDeviceService;

    /**
     * 监控模块、数据查询通用接口
     *
     * @param deviceEnum 设备类型 STATION：电站，BMS，PCS，ESUNIT：储能单元，METER：电表
     * @param dateTime   时间（为空则查询当前时间，格式：yyyy-MM-dd HH:mm:ss）
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.sm.domain.vo.DataMonitorVo>>
     * <AUTHOR>
     * @date 2025/04/15 10:52
     */
    @GetMapping("/listDataMonitor")
    public R<List<DataMonitorVo>> listDataMonitor(@RequestParam SmDeviceEnum deviceEnum,
                                                  @RequestParam(required = false) String dateTime,
                                                  @RequestParam(required = false) String deviceCode) {
        List<DataMonitorVo> voList = dataMonitorService.listDataMonitor(deviceEnum, dateTime, deviceCode);
        return R.ok(voList);
    }

    /**
     * 分页监控模块、数据查询通用接口
     *
     * @param deviceEnum 设备类型 STATION：电站，BMS，PCS，ESUNIT：储能单元，METER：电表
     * @param dateTime   时间（为空则查询当前时间，格式：yyyy-MM-dd HH:mm:ss）
     * @param deviceCode 设备编码
     * @param current    当前页码
     * @param size       每页条数
     * @return com.zjwly.common.core.domain.R<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.zjwly.sm.domain.vo.DataMonitorVo>>
     * <AUTHOR>
     * @date 2025/04/30 18:26
     */
    @GetMapping("/pageDataMonitor")
    public R<Page<DataMonitorVo>> pageDataMonitor(@RequestParam SmDeviceEnum deviceEnum,
                                                  @RequestParam(required = false) String dateTime,
                                                  @RequestParam(required = false) String deviceCode,
                                                  @RequestParam(required = false, defaultValue = "-1") Long current,
                                                  @RequestParam(required = false, defaultValue = "-1") Long size) {
        Page<DataMonitorVo> page = dataMonitorService.listDataMonitor(deviceEnum, dateTime, deviceCode, current, size);
        return R.ok(page);
    }

    /**
     * 监控区间数据
     *
     * @param sectionBo
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.sm.domain.vo.SectionVo>>
     * <AUTHOR>
     * @date 2025/04/18 11:10
     */
    @PostMapping("/section")
    public R<List<SectionVo>> section(@RequestBody SectionBo sectionBo) {
        List<SectionVo> vos = dataMonitorService.section(sectionBo);
        return R.ok(vos);
    }

    /**
     * 监控区间数据下拉框
     *
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.sm.domain.vo.CascaderVo>>
     * <AUTHOR>
     * @date 2025/04/23 09:51
     */
    @GetMapping("/sectionCascader")
    public R<List<CascaderVo>> sectionCascader() {
        List<CascaderVo> cascaderVos = dataMonitorService.sectionCascader();
        return R.ok(cascaderVos);
    }

    /**
     * 获取设备列表
     *
     * @param pid 父ID 0为查询顶级设备
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.sm.domain.SmDevice>>
     * <AUTHOR>
     * @date 2025/04/23 09:51
     */
    @GetMapping("/listDevice")
    public R<List<SmDevice>> listDevice(@RequestParam("pid") Long pid) {
        List<SmDevice> devices = smDeviceService.lambdaQuery().eq(SmDevice::getDevicePid, pid)
            .eq(SmDevice::getStatus, SmStatusEnum.OK.getCode()).orderByAsc(SmDevice::getSort).list();
        return R.ok(devices);
    }

    /**
     * 根据父级设备编码获取子级设备列表
     *
     * @param deviceEnum 设备类型 STATION：电站，BMS，PCS，ESUNIT：储能单元，METER：电表
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.sm.domain.SmDevice>>
     * <AUTHOR>
     * @date 2025/04/28 14:40
     */
    @GetMapping("/listDeviceByCode")
    public R<List<SmDevice>> listDeviceByCode(@RequestParam SmDeviceEnum deviceEnum) {
        List<SmDevice> devices = dataMonitorService.listDeviceByCode(deviceEnum);
        return R.ok(devices);
    }

    /**
     * 懒加载获取级联菜单
     *
     * @param level 级别 1,2,3
     * @param value 父级设备编码，1级时为null
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.sm.domain.vo.CascaderVo>>
     * <AUTHOR>
     * @date 2025/04/28 14:40
     */
    @GetMapping("/sectionCascaderLazy")
    public R<List<CascaderVo>> sectionCascaderLazy(@RequestParam Integer level,
                                                   @RequestParam(required = false) String value) {
        List<CascaderVo> cascaderVos = dataMonitorService.sectionCascaderLazy(level, value);
        return R.ok(cascaderVos);
    }

    /**
     * 接线图数据
     *
     * @return com.zjwly.common.core.domain.R<java.util.Map < java.lang.Long, com.zjwly.sm.domain.vo.WiringDiagramDataVo>>
     * <AUTHOR>
     * @date 2025/04/30 15:11
     */
    @GetMapping("/getWiringDiagramData")
    public R<Map<Long, WiringDiagramDataVo>> getWiringDiagramData() {
        Map<Long, WiringDiagramDataVo> map = dataMonitorService.getWiringDiagramData();
        return R.ok(map);
    }
}
