package com.zjwly.sm.controller;

import com.zjwly.common.core.domain.R;
import com.zjwly.sm.domain.vo.HomepageASeriesVo;
import com.zjwly.sm.domain.vo.HomepagePowerACSeriesVo;
import com.zjwly.sm.domain.vo.RunningStatusVo;
import com.zjwly.sm.domain.vo.SmHomepageVo;
import com.zjwly.sm.service.HomepageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 *
 * <AUTHOR>
 * @date 2025/04/23 14:01
 */
@RestController
@RequestMapping("/ds/sm/homepage")
@RequiredArgsConstructor
public class HomepageController {
    private final HomepageService homepageService;

    /**
     * 首页
     *
     * @return com.zjwly.common.core.domain.R<com.zjwly.sm.domain.vo.SmHomepageVo>
     * <AUTHOR>
     * @date 2025/04/24 11:10
     */
    @GetMapping("/homepage")
    public R<SmHomepageVo> homepage() {
        return R.ok(homepageService.homepage());
    }

    /**
     * 顶栏状态
     *
     * @return com.zjwly.common.core.domain.R<com.zjwly.sm.domain.vo.RunningStatusVo>
     * <AUTHOR>
     * @date 2025/04/27 19:21
     */
    @GetMapping("/runningStatus")
    public R<RunningStatusVo> runningStatus() {
        return R.ok(homepageService.runningStatus());
    }

    /**
     * 交流有功、无功功率曲线
     *
     * @param deviceCode 设备编号
     * @return com.zjwly.common.core.domain.R<com.zjwly.sm.domain.vo.HomepagePowerACSeriesVo>
     * <AUTHOR>
     * @date 2025/05/06 14:24
     */
    @GetMapping("/getPowerACSeries")
    public R<HomepagePowerACSeriesVo> getPowerACSeries(String deviceCode) {
        return R.ok(homepageService.getPowerACSeries(deviceCode));
    }

    /**
     * A相电压、电流曲线
     *
     * @param deviceCode 设备编号
     * @return com.zjwly.common.core.domain.R<com.zjwly.sm.domain.vo.HomepageASeriesVo>
     * <AUTHOR>
     * @date 2025/05/06 14:24
     */
    @GetMapping("/getASeries")
    public R<HomepageASeriesVo> getASeries(String deviceCode) {
        return R.ok(homepageService.getASeries(deviceCode));
    }
}
