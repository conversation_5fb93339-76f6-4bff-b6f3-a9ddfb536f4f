package com.zjwly.sm.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 采集间隔配置类
 *
 * <AUTHOR>
 * @date 2025/05/06 13:54
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "interval")
public class IntervalProperties {
    /**
     * 采集间隔
     */
    private Integer value;
    /**
     * 采集间隔单位
     */
    private Integer unit;
}
