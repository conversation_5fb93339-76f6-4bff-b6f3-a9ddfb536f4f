package com.zjwly.sm.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjwly.common.core.domain.R;
import com.zjwly.sm.domain.SmAlarmLevelConfig;
import com.zjwly.sm.domain.bo.AlarmLevelBo;
import com.zjwly.sm.domain.vo.AlarmInfoVo;
import com.zjwly.sm.service.SmAlarmInfoService;
import com.zjwly.sm.service.SmAlarmLevelConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 告警提示
 *
 * <AUTHOR>
 * @date 2025/04/26 09:16
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ds/sm/alarm")
public class AlarmController {
    private final SmAlarmLevelConfigService smAlarmLevelConfigService;
    private final SmAlarmInfoService smAlarmInfoService;

    /**
     * 告警提示配置
     *
     * @param name    名称
     * @param current 当前页码
     * @param size    每页数量
     * @return com.zjwly.common.core.domain.R<com.baomidou.mybatisplus.core.metadata.IPage < com.zjwly.sm.domain.SmAlarmLevelConfig>>
     * <AUTHOR>
     * @date 2025/04/26 09:28
     */
    @GetMapping("/listConfig")
    public R<IPage<SmAlarmLevelConfig>> listConfig(@RequestParam(required = false) String name,
                                                   @RequestParam(required = false, defaultValue = "1") int current,
                                                   @RequestParam(required = false, defaultValue = "10") int size) {
        IPage<SmAlarmLevelConfig> page = new Page<>(current, size);
        IPage<SmAlarmLevelConfig> alarmLevelConfigPage = smAlarmLevelConfigService.page(page,
            Wrappers.<SmAlarmLevelConfig>lambdaQuery().gt(SmAlarmLevelConfig::getId, 0)
                .like(StrUtil.isNotBlank(name), SmAlarmLevelConfig::getName, name));
        return R.ok(alarmLevelConfigPage);
    }

    /**
     * 告警提示列表
     *
     * @param occurStartTime 告警发生时间-开始
     * @param occurEndTime   告警发生时间-结束
     * @param overStartTime  告警持续时间-开始
     * @param overEndTime    告警持续时间-结束
     * @param deviceName     设备名称
     * @param alarmName      告警名称
     * @param current        当前页码
     * @param size           每页数量
     * @return com.zjwly.common.core.domain.R<com.baomidou.mybatisplus.core.metadata.IPage < com.zjwly.sm.domain.vo.AlarmInfoVo>>
     * <AUTHOR>
     * @date 2025/04/26 09:28
     */
    @GetMapping("/listAlarm")
    public R<IPage<AlarmInfoVo>> listAlarm(
        @RequestParam(name = "occurStartTime", required = false) String occurStartTime,
        @RequestParam(name = "occurEndTime", required = false) String occurEndTime,
        @RequestParam(name = "overStartTime", required = false) String overStartTime,
        @RequestParam(name = "overEndTime", required = false) String overEndTime,
        @RequestParam(name = "deviceName", required = false) String deviceName,
        @RequestParam(name = "alarmName", required = false) String alarmName,
        @RequestParam(name = "current", required = false, defaultValue = "1") Integer current,
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size) {
        IPage<AlarmInfoVo> page = smAlarmInfoService.listAlarmInfo(new Page<>(current, size), occurStartTime, occurEndTime, overStartTime, overEndTime, deviceName, alarmName);
        return R.ok(page);
    }

    /**
     * 更新告警提示等级
     *
     * @param bo 告警提示等级
     * @return com.zjwly.common.core.domain.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/04/28 09:29
     */
    @PutMapping("/updateLevel")
    public R<Boolean> updateLevel(@RequestBody AlarmLevelBo bo) {
        smAlarmLevelConfigService.lambdaUpdate().eq(SmAlarmLevelConfig::getId, bo.getId())
            .set(SmAlarmLevelConfig::getLevel, bo.getLevel())
            .update();
        return R.ok();
    }
}
