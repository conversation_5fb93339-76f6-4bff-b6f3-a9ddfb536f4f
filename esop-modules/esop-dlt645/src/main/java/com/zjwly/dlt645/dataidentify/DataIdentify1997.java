package com.zjwly.dlt645.dataidentify;

import java.util.*;


public class DataIdentify1997 {
    // 常量定义
    private static final byte[] POSITIVE_TOTAL_POWER = new byte[]{(byte)0x10,(byte)0x90}; // 正向有功总电能
    private static final byte[] NEGATIVE_TOTAL_POWER = new byte[]{(byte)0x20,(byte)0x90}; // 反向有功总电能
    private static final byte[] A_VOLTAGE = new byte[]{(byte)0x11,(byte)0xB6}; // A相电压
    private static final byte[] B_VOLTAGE = new byte[]{(byte)0x12,(byte)0xB6}; // B相电压
    private static final byte[] C_VOLTAGE = new byte[]{(byte)0x13,(byte)0xB6}; // C相电压
    private static final byte[] A_CURRENT = new byte[]{(byte)0x21,(byte)0xB6}; // A相电流
    private static final byte[] B_CURRENT = new byte[]{(byte)0x22,(byte)0xB6}; // B相电流
    private static final byte[] C_CURRENT = new byte[]{(byte)0x23,(byte)0xB6}; // C相电流
    private static final byte[] INSTANT_TOTAL_POWER = new byte[]{(byte)0x30,(byte)0xB6}; // 瞬时总功率
    private static final byte[] INSTANT_A_ACTIVE_POWER = new byte[]{(byte)0x31,(byte)0xB6}; // 瞬时A相有功功率
    private static final byte[] INSTANT_B_ACTIVE_POWER = new byte[]{(byte)0x32,(byte)0xB6}; // 瞬时B相有功功率
    private static final byte[] INSTANT_C_ACTIVE_POWER = new byte[]{(byte)0x33,(byte)0xB6}; // 瞬时C相有功功率
    private static final byte[] INSTANT_TOTAL_REACTIVE_POWER = new byte[]{(byte)0x40,(byte)0xB6}; // 瞬时总无功功率
    private static final byte[] INSTANT_A_REACTIVE_POWER = new byte[]{(byte)0x41,(byte)0xB6}; // 瞬时A相总无功功率
    private static final byte[] INSTANT_B_REACTIVE_POWER = new byte[]{(byte)0x42,(byte)0xB6}; // 瞬时B相总无功功率
    private static final byte[] INSTANT_C_REACTIVE_POWER = new byte[]{(byte)0x43,(byte)0xB6}; // 瞬时C相总无功功率
    private static final byte[] TOTAL_POWER_FACTOR = new byte[]{(byte)0x50,(byte)0xB6}; // 总功率因数
    private static final byte[] A_POWER_FACTOR = new byte[]{(byte)0x51,(byte)0xB6}; // A相功率因数
    private static final byte[] B_POWER_FACTOR = new byte[]{(byte)0x52,(byte)0xB6}; // B相功率因数
    private static final byte[] C_POWER_FACTOR = new byte[]{(byte)0x53,(byte)0xB6}; // C相功率因数

    private List<byte[]> dataIdent = new ArrayList<>();
    private Map<String ,String> identifyname=new HashMap<String ,String>();
    private Map<String ,Integer> doc=new HashMap<String ,Integer>();
    /**为了保证各个通道的协调，需要增加几条重复的命令帧，选择瞬时项**/
    public DataIdentify1997() {
        dataIdent.add(POSITIVE_TOTAL_POWER);//正向有功总电能
        dataIdent.add(NEGATIVE_TOTAL_POWER);//反向有功总电能
        dataIdent.add(A_VOLTAGE);//A相电压
        dataIdent.add(B_VOLTAGE);//B相电压
        dataIdent.add(C_VOLTAGE);//C相电压
        dataIdent.add(A_CURRENT);//A相电流
        dataIdent.add(B_CURRENT);//B相电流
        dataIdent.add(C_CURRENT);//C相电流
        dataIdent.add(INSTANT_TOTAL_POWER);//瞬时总功率
        dataIdent.add(INSTANT_A_ACTIVE_POWER);//瞬时A相有功功率
        dataIdent.add(INSTANT_B_ACTIVE_POWER);//瞬时B相有功功率
        dataIdent.add(INSTANT_C_ACTIVE_POWER);//瞬时C相有功功率
        dataIdent.add(INSTANT_TOTAL_REACTIVE_POWER);//瞬时总无功功率
        dataIdent.add(INSTANT_A_REACTIVE_POWER);//瞬时A相总无功功率
        dataIdent.add(INSTANT_B_REACTIVE_POWER);//瞬时B相总无功功率
        dataIdent.add(INSTANT_C_REACTIVE_POWER);//瞬时C相总无功功率
        dataIdent.add(TOTAL_POWER_FACTOR);//总功率因数
        dataIdent.add(A_POWER_FACTOR);//A相功率因数
        dataIdent.add(B_POWER_FACTOR);//B相功率因数
        dataIdent.add(C_POWER_FACTOR);//C相功率因数
        //增加重复项
        dataIdent.add(INSTANT_TOTAL_POWER);//瞬时总功率
        dataIdent.add(INSTANT_A_ACTIVE_POWER);//瞬时A相有功功率
        dataIdent.add(INSTANT_B_ACTIVE_POWER);//瞬时B相有功功率
        dataIdent.add(INSTANT_C_ACTIVE_POWER);//瞬时C相有功功率
        dataIdent.add(INSTANT_TOTAL_REACTIVE_POWER);//瞬时总无功功率

        identifyname.put(Arrays.toString(POSITIVE_TOTAL_POWER),"total_power");//正向有功总电能
        identifyname.put(Arrays.toString(NEGATIVE_TOTAL_POWER),"neg_positive_power");//反向有功总电能
        identifyname.put(Arrays.toString(A_VOLTAGE),"a_voltage");//A相电压
        identifyname.put(Arrays.toString(B_VOLTAGE),"b_voltage");//B相电压
        identifyname.put(Arrays.toString(C_VOLTAGE),"c_voltage");//C相电压
        identifyname.put(Arrays.toString(A_CURRENT),"a_current");//A相电流
        identifyname.put(Arrays.toString(B_CURRENT),"b_current");//B相电流
        identifyname.put(Arrays.toString(C_CURRENT),"c_current");//C相电流
        identifyname.put(Arrays.toString(INSTANT_TOTAL_POWER),"positive_power");//瞬时有功总功率
        identifyname.put(Arrays.toString(INSTANT_A_ACTIVE_POWER),"a_positive_power");//瞬时A相有功功率
        identifyname.put(Arrays.toString(INSTANT_B_ACTIVE_POWER),"b_positive_power");//瞬时B相有功功率
        identifyname.put(Arrays.toString(INSTANT_C_ACTIVE_POWER),"c_positive_power");//瞬时C相有功功率
        identifyname.put(Arrays.toString(INSTANT_TOTAL_REACTIVE_POWER),"reactive_power");//瞬时总无功功率
        identifyname.put(Arrays.toString(INSTANT_A_REACTIVE_POWER),"a_reactive_power");//瞬时A相总无功功率
        identifyname.put(Arrays.toString(INSTANT_B_REACTIVE_POWER),"b_reactive_power");//瞬时B相总无功功率
        identifyname.put(Arrays.toString(INSTANT_C_REACTIVE_POWER),"c_reactive_power");//瞬时C相总无功功率
        identifyname.put(Arrays.toString(TOTAL_POWER_FACTOR),"influence");//总功率因数
        identifyname.put(Arrays.toString(A_POWER_FACTOR),"a_influence");//A相功率因数
        identifyname.put(Arrays.toString(B_POWER_FACTOR),"b_influence");//B相功率因数
        identifyname.put(Arrays.toString(C_POWER_FACTOR),"c_influence");//C相功率因数
        //增加重复项
        identifyname.put(Arrays.toString(INSTANT_TOTAL_POWER),"positive_power");//瞬时有功总功率
        identifyname.put(Arrays.toString(INSTANT_A_ACTIVE_POWER),"a_positive_power");//瞬时A相有功功率
        identifyname.put(Arrays.toString(INSTANT_B_ACTIVE_POWER),"b_positive_power");//瞬时B相有功功率
        identifyname.put(Arrays.toString(INSTANT_C_ACTIVE_POWER),"c_positive_power");//瞬时C相有功功率
        identifyname.put(Arrays.toString(INSTANT_TOTAL_REACTIVE_POWER),"reactive_power");//瞬时总无功功率

        doc.put(Arrays.toString(POSITIVE_TOTAL_POWER),2);//正向有功总电能
        doc.put(Arrays.toString(NEGATIVE_TOTAL_POWER),2);//反向有功总电能
        doc.put(Arrays.toString(A_VOLTAGE),0);//A相电压
        doc.put(Arrays.toString(B_VOLTAGE),0);//B相电压
        doc.put(Arrays.toString(C_VOLTAGE),0);//C相电压
        doc.put(Arrays.toString(A_CURRENT),2);//A相电流
        doc.put(Arrays.toString(B_CURRENT),2);//B相电流
        doc.put(Arrays.toString(C_CURRENT),2);//C相电流
        doc.put(Arrays.toString(INSTANT_TOTAL_POWER),4);//瞬时有功总功率
        doc.put(Arrays.toString(INSTANT_A_ACTIVE_POWER),4);//瞬时A相有功功率
        doc.put(Arrays.toString(INSTANT_B_ACTIVE_POWER),4);//瞬时B相有功功率
        doc.put(Arrays.toString(INSTANT_C_ACTIVE_POWER),4);//瞬时C相有功功率
        doc.put(Arrays.toString(INSTANT_TOTAL_REACTIVE_POWER),2);//瞬时总无功功率
        doc.put(Arrays.toString(INSTANT_A_REACTIVE_POWER),2);//瞬时A相总无功功率
        doc.put(Arrays.toString(INSTANT_B_REACTIVE_POWER),2);//瞬时B相总无功功率
        doc.put(Arrays.toString(INSTANT_C_REACTIVE_POWER),2);//瞬时C相总无功功率
        doc.put(Arrays.toString(TOTAL_POWER_FACTOR),3);//总功率因数
        doc.put(Arrays.toString(A_POWER_FACTOR),3);//A相功率因数
        doc.put(Arrays.toString(B_POWER_FACTOR),3);//B相功率因数
        doc.put(Arrays.toString(C_POWER_FACTOR),3);//C相功率因数
        //增加重复项
        doc.put(Arrays.toString(INSTANT_TOTAL_POWER),4);//瞬时有功总功率
        doc.put(Arrays.toString(INSTANT_A_ACTIVE_POWER),4);//瞬时A相有功功率
        doc.put(Arrays.toString(INSTANT_B_ACTIVE_POWER),4);//瞬时B相有功功率
        doc.put(Arrays.toString(INSTANT_C_ACTIVE_POWER),4);//瞬时C相有功功率
        doc.put(Arrays.toString(INSTANT_TOTAL_REACTIVE_POWER),2);//瞬时总无功功率
    }
    public int getLength()
    {
        return identifyname.size();
    }

    public List<byte[]> getDataIdent() {
        return dataIdent;
    }

    public Map<String, Integer> getDoc() {
        return doc;
    }

    public Map<String, String> getIdentifyname() {
        return identifyname;
    }
}

