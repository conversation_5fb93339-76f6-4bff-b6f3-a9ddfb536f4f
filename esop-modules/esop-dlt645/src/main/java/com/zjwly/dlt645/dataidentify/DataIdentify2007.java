package com.zjwly.dlt645.dataidentify;

import java.util.*;


public class DataIdentify2007 {
    // 常量定义
    private static final byte[] TOTAL_POWER = new byte[]{(byte)0x00,(byte)0x00,(byte)0x00,(byte)0x00}; // 有功总电能
    private static final byte[] POSITIVE_TOTAL_POWER = new byte[]{(byte)0x00,(byte)0x00,(byte)0x01,(byte)0x00}; // 正向有功总电能
    private static final byte[] NEGATIVE_TOTAL_POWER = new byte[]{(byte)0x00,(byte)0x00,(byte)0x02,(byte)0x00}; // 反向有功总电能
    private static final byte[] A_VOLTAGE = new byte[]{(byte)0x00,(byte)0x01,(byte)0x01,(byte)0x02}; // A相电压
    private static final byte[] B_VOLTAGE = new byte[]{(byte)0x00,(byte)0x02,(byte)0x01,(byte)0x02}; // B相电压
    private static final byte[] C_VOLTAGE = new byte[]{(byte)0x00,(byte)0x03,(byte)0x01,(byte)0x02}; // C相电压
    private static final byte[] A_CURRENT = new byte[]{(byte)0x00,(byte)0x01,(byte)0x02,(byte)0x02}; // A相电流
    private static final byte[] B_CURRENT = new byte[]{(byte)0x00,(byte)0x02,(byte)0x02,(byte)0x02}; // B相电流
    private static final byte[] C_CURRENT = new byte[]{(byte)0x00,(byte)0x03,(byte)0x02,(byte)0x02}; // C相电流
    private static final byte[] INSTANT_ACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x00,(byte)0x03,(byte)0x02}; // 瞬时有功功率
    private static final byte[] INSTANT_A_ACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x01,(byte)0x03,(byte)0x02}; // 瞬时A相有功功率
    private static final byte[] INSTANT_B_ACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x02,(byte)0x03,(byte)0x02}; // 瞬时B相有功功率
    private static final byte[] INSTANT_C_ACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x03,(byte)0x03,(byte)0x02}; // 瞬时C相有功功率
    private static final byte[] INSTANT_REACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x00,(byte)0x04,(byte)0x02}; // 瞬时无功功率
    private static final byte[] INSTANT_A_REACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x01,(byte)0x04,(byte)0x02}; // 瞬时A相总无功功率
    private static final byte[] INSTANT_B_REACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x02,(byte)0x04,(byte)0x02}; // 瞬时B相总无功功率
    private static final byte[] INSTANT_C_REACTIVE_POWER = new byte[]{(byte)0x00,(byte)0x03,(byte)0x04,(byte)0x02}; // 瞬时C相总无功功率
    private static final byte[] INSTANT_APPARENT_POWER = new byte[]{(byte)0x00,(byte)0x00,(byte)0x05,(byte)0x02}; // 瞬时视在功率
    private static final byte[] A_APPARENT_POWER = new byte[]{(byte)0x00,(byte)0x01,(byte)0x05,(byte)0x02}; // A相视在功率
    private static final byte[] B_APPARENT_POWER = new byte[]{(byte)0x00,(byte)0x02,(byte)0x05,(byte)0x02}; // B相视在功率
    private static final byte[] C_APPARENT_POWER = new byte[]{(byte)0x00,(byte)0x03,(byte)0x05,(byte)0x02}; // C相视在功率
    private static final byte[] TOTAL_POWER_FACTOR = new byte[]{(byte)0x00,(byte)0x00,(byte)0x06,(byte)0x02}; // 总功率因数
    private static final byte[] A_POWER_FACTOR = new byte[]{(byte)0x00,(byte)0x01,(byte)0x06,(byte)0x02}; // A相功率因数
    private static final byte[] B_POWER_FACTOR = new byte[]{(byte)0x00,(byte)0x02,(byte)0x06,(byte)0x02}; // B相功率因数
    private static final byte[] C_POWER_FACTOR = new byte[]{(byte)0x00,(byte)0x03,(byte)0x06,(byte)0x02}; // C相功率因数

    public List<byte[]> dataIdent = new ArrayList<>();
    public Map<String,String> identifyname=new HashMap<String,String>();
    public Map<String,Integer> doc=new HashMap<String,Integer>();
    public int length;
    public DataIdentify2007() {
        dataIdent.add(0,TOTAL_POWER);//有功总电能

        dataIdent.add(1,POSITIVE_TOTAL_POWER);//正向有功总电能
        dataIdent.add(2,NEGATIVE_TOTAL_POWER);//反向有功总电能

        dataIdent.add(3,A_VOLTAGE);//A相电压
        dataIdent.add(4,B_VOLTAGE);//B相电压
        dataIdent.add(5,C_VOLTAGE);//C相电压

        dataIdent.add(6,A_CURRENT);//A相电流
        dataIdent.add(7,B_CURRENT);//B相电流
        dataIdent.add(8,C_CURRENT);//C相电流

        dataIdent.add(9,INSTANT_ACTIVE_POWER);//瞬时有功功率
        dataIdent.add(10,INSTANT_A_ACTIVE_POWER);//瞬时A相有功功率
        dataIdent.add(11,INSTANT_B_ACTIVE_POWER);//瞬时B相有功功率
        dataIdent.add(12,INSTANT_C_ACTIVE_POWER);//瞬时C相有功功率

        dataIdent.add(13,INSTANT_REACTIVE_POWER);//瞬时无功功率
        dataIdent.add(14,INSTANT_A_REACTIVE_POWER);//瞬时A相总无功功率
        dataIdent.add(15,INSTANT_B_REACTIVE_POWER);//瞬时B相总无功功率
        dataIdent.add(16,INSTANT_C_REACTIVE_POWER);//瞬时C相总无功功率

        dataIdent.add(17,INSTANT_APPARENT_POWER);//瞬时视在功率
        dataIdent.add(18,A_APPARENT_POWER);//A相视在功率
        dataIdent.add(19,B_APPARENT_POWER);//B相视在功率
        dataIdent.add(20,C_APPARENT_POWER);//C相视在功率

        dataIdent.add(21,TOTAL_POWER_FACTOR);//总功率因数
        dataIdent.add(22,A_POWER_FACTOR);//A相功率因数
        dataIdent.add(23,B_POWER_FACTOR);//B相功率因数
        dataIdent.add(24,C_POWER_FACTOR);//C相功率因数

        identifyname.put(Arrays.toString(TOTAL_POWER),"total_power");
        identifyname.put(Arrays.toString(POSITIVE_TOTAL_POWER),"pos_positive_power");
        identifyname.put(Arrays.toString(NEGATIVE_TOTAL_POWER),"neg_positive_power");
        identifyname.put(Arrays.toString(A_VOLTAGE),"a_voltage");
        identifyname.put(Arrays.toString(B_VOLTAGE),"b_voltage");
        identifyname.put(Arrays.toString(C_VOLTAGE),"c_voltage");
        identifyname.put(Arrays.toString(A_CURRENT),"a_current");
        identifyname.put(Arrays.toString(B_CURRENT),"b_current");
        identifyname.put(Arrays.toString(C_CURRENT),"c_current");
        identifyname.put(Arrays.toString(INSTANT_ACTIVE_POWER),"positive_power");
        identifyname.put(Arrays.toString(INSTANT_A_ACTIVE_POWER),"a_positive_power");
        identifyname.put(Arrays.toString(INSTANT_B_ACTIVE_POWER),"b_positive_power");
        identifyname.put(Arrays.toString(INSTANT_C_ACTIVE_POWER),"c_positive_power");
        identifyname.put(Arrays.toString(INSTANT_REACTIVE_POWER),"reactive_power");
        identifyname.put(Arrays.toString(INSTANT_A_REACTIVE_POWER),"a_reactive_power");
        identifyname.put(Arrays.toString(INSTANT_B_REACTIVE_POWER),"b_reactive_power");
        identifyname.put(Arrays.toString(INSTANT_C_REACTIVE_POWER),"c_reactive_power");
        identifyname.put(Arrays.toString(INSTANT_APPARENT_POWER),"apparent_power");
        identifyname.put(Arrays.toString(A_APPARENT_POWER),"a_apparent_power");
        identifyname.put(Arrays.toString(B_APPARENT_POWER),"b_apparent_power");
        identifyname.put(Arrays.toString(C_APPARENT_POWER),"c_apparent_power");
        identifyname.put(Arrays.toString(TOTAL_POWER_FACTOR),"influence");
        identifyname.put(Arrays.toString(A_POWER_FACTOR),"a_influence");
        identifyname.put(Arrays.toString(B_POWER_FACTOR),"b_influence");
        identifyname.put(Arrays.toString(C_POWER_FACTOR),"c_influence");
        doc.put(Arrays.toString(TOTAL_POWER),2);
        doc.put(Arrays.toString(POSITIVE_TOTAL_POWER),2);
        doc.put(Arrays.toString(NEGATIVE_TOTAL_POWER),2);
        doc.put(Arrays.toString(A_VOLTAGE),1);
        doc.put(Arrays.toString(B_VOLTAGE),1);
        doc.put(Arrays.toString(C_VOLTAGE),1);
        doc.put(Arrays.toString(A_CURRENT),3);
        doc.put(Arrays.toString(B_CURRENT),3);
        doc.put(Arrays.toString(C_CURRENT),3);
        doc.put(Arrays.toString(INSTANT_ACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_A_ACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_B_ACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_C_ACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_REACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_A_REACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_B_REACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_C_REACTIVE_POWER),4);
        doc.put(Arrays.toString(INSTANT_APPARENT_POWER),4);
        doc.put(Arrays.toString(A_APPARENT_POWER),4);
        doc.put(Arrays.toString(B_APPARENT_POWER),4);
        doc.put(Arrays.toString(C_APPARENT_POWER),4);
        doc.put(Arrays.toString(TOTAL_POWER_FACTOR),4);
        doc.put(Arrays.toString(A_POWER_FACTOR),4);
        doc.put(Arrays.toString(B_POWER_FACTOR),4);
        doc.put(Arrays.toString(C_POWER_FACTOR),4);
    }
    public int  getLength()
    {
        return dataIdent.size();
    }
}
