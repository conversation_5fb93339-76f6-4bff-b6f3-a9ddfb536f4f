package com.zjwly.dlt645.dataidentify;

import java.util.*;

/**
 * 广东
 *
 * <AUTHOR>
 * @date 2025/08/04 11:38
 */
public class DataIdentifyGD {
    // 常量定义 - 基本信息类 (02 25 01 XX)
    /**
     * 聚合用户数量（户）
     */
    private static final byte[] AGGREGATE_USER_NUMBER = new byte[]{0x00, 0x01, 0x25, 0x02};

    /**
     * 聚合容量（kW）
     */
    private static final byte[] AGGREGATE_CAPACITY = new byte[]{0x01, 0x01, 0x25, 0x02};

    /**
     * 调节容量(kW)
     */
    private static final byte[] REGULATION_CAPACITY = new byte[]{0x02, 0x01, 0x25, 0x02};

    /**
     * 最大上调容量(kW)
     */
    private static final byte[] MAX_UP_REGULATION_CAPACITY = new byte[]{0x03, 0x01, 0x25, 0x02};

    /**
     * 最大下调容量(kW)
     */
    private static final byte[] MAX_DOWN_REGULATION_CAPACITY = new byte[]{0x04, 0x01, 0x25, 0x02};

    /**
     * 调节持续时间(min)
     */
    private static final byte[] REGULATION_DURATION = new byte[]{0x05, 0x01, 0x25, 0x02};

    /**
     * 最大上调节容量持续时间(min)
     */
    private static final byte[] MAX_UP_REGULATION_DURATION = new byte[]{0x06, 0x01, 0x25, 0x02};

    /**
     * 最大下调节容量持续时间(min)
     */
    private static final byte[] MAX_DOWN_REGULATION_DURATION = new byte[]{0x07, 0x01, 0x25, 0x02};

    /**
     * 调节速率
     */
    private static final byte[] REGULATION_RATE = new byte[]{0x08, 0x01, 0x25, 0x02};

    /**
     * 上调速率
     */
    private static final byte[] UP_REGULATION_RATE = new byte[]{0x09, 0x01, 0x25, 0x02};

    /**
     * 下调速率
     */
    private static final byte[] DOWN_REGULATION_RATE = new byte[]{0x0a, 0x01, 0x25, 0x02};

    /**
     * 调节精度
     */
    private static final byte[] REGULATION_PRECISION = new byte[]{0x0b, 0x01, 0x25, 0x02};

    /**
     * 控制准确度
     */
    private static final byte[] CONTROL_ACCURACY = new byte[]{0x0c, 0x01, 0x25, 0x02};

    /**
     * 爬坡速度
     */
    private static final byte[] RAMP_SPEED = new byte[]{0x0d, 0x01, 0x25, 0x02};

    /**
     * 上调爬坡速度
     */
    private static final byte[] UP_RAMP_SPEED = new byte[]{0x0e, 0x01, 0x25, 0x02};

    /**
     * 下调爬坡速度
     */
    private static final byte[] DOWN_RAMP_SPEED = new byte[]{0x0f, 0x01, 0x25, 0x02};

    /**
     * 爬坡时长
     */
    private static final byte[] RAMP_DURATION = new byte[]{0x12, 0x01, 0x25, 0x02};

    /**
     * 上调爬坡时长
     */
    private static final byte[] UP_RAMP_DURATION = new byte[]{0x13, 0x01, 0x25, 0x02};

    /**
     * 下调爬坡时长
     */
    private static final byte[] DOWN_RAMP_DURATION = new byte[]{0x14, 0x01, 0x25, 0x02};

    /**
     * 虚拟电厂编号
     */
    private static final byte[] VPP_CODE = new byte[]{0x15, 0x01, 0x25, 0x02};

    /**
     * 虚拟电厂中文
     */
    private static final byte[] VPP_NAME_CN = new byte[]{0x16, 0x01, 0x25, 0x02};

    /**
     * 参与交易品种
     */
    private static final byte[] TRADING_VARIETIES = new byte[]{0x17, 0x01, 0x25, 0x02};

    /**
     * 聚合资源类型
     */
    private static final byte[] RESOURCE_TYPES = new byte[]{0x18, 0x01, 0x25, 0x02};

    /**
     * 调控开始时间
     */
    private static final byte[] CONTROL_START_TIME = new byte[]{0x19, 0x01, 0x25, 0x02};

    /**
     * 调控结束时间
     */
    private static final byte[] CONTROL_END_TIME = new byte[]{0x1a, 0x01, 0x25, 0x02};

    /**
     * 目标调控负荷
     */
    private static final byte[] TARGET_CONTROL_LOAD = new byte[]{0x1b, 0x01, 0x25, 0x02};

    // 用户编号信息类 (02 25 02 XX)
    /**
     * 用户编号信息(用户1-20)
     */
    private static final byte[] USER_INFO_01_20 = new byte[]{0x01, 0x02, 0x25, 0x02};

    /**
     * 用户编号信息(用户21-40)
     */
    private static final byte[] USER_INFO_21_40 = new byte[]{0x02, 0x02, 0x25, 0x02};

    /**
     * 用户编号信息(用户41-60)
     */
    private static final byte[] USER_INFO_41_60 = new byte[]{0x03, 0x02, 0x25, 0x02};

    /**
     * 用户编号信息(用户61-80)
     */
    private static final byte[] USER_INFO_61_80 = new byte[]{0x04, 0x02, 0x25, 0x02};

    /**
     * 用户编号信息(用户81-100)
     */
    private static final byte[] USER_INFO_81_100 = new byte[]{0x05, 0x02, 0x25, 0x02};

    // 用户功率信息类 (02 25 03 XX)
    /**
     * 用户功率信息(用户1-20)
     */
    private static final byte[] USER_POWER_01_20 = new byte[]{0x01, 0x03, 0x25, 0x02};

    /**
     * 用户功率信息(用户21-40)
     */
    private static final byte[] USER_POWER_21_40 = new byte[]{0x02, 0x03, 0x25, 0x02};

    /**
     * 用户功率信息(用户41-60)
     */
    private static final byte[] USER_POWER_41_60 = new byte[]{0x03, 0x03, 0x25, 0x02};

    /**
     * 用户功率信息(用户61-80)
     */
    private static final byte[] USER_POWER_61_80 = new byte[]{0x04, 0x03, 0x25, 0x02};

    /**
     * 用户功率信息(用户81-100)
     */
    private static final byte[] USER_POWER_81_100 = new byte[]{0x05, 0x03, 0x25, 0x02};

    public List<byte[]> dataIdent = new ArrayList<>();
    public Map<String, String> identifyname = new HashMap<String, String>();
    public Map<String, Integer> doc = new HashMap<String, Integer>();
    public int length;

    public DataIdentifyGD() {
        // 基本信息类数据标识
        dataIdent.add(AGGREGATE_USER_NUMBER);
        dataIdent.add(AGGREGATE_CAPACITY);
        dataIdent.add(REGULATION_CAPACITY);
        dataIdent.add(MAX_UP_REGULATION_CAPACITY);
        dataIdent.add(MAX_DOWN_REGULATION_CAPACITY);
        dataIdent.add(REGULATION_DURATION);
        dataIdent.add(MAX_UP_REGULATION_DURATION);
        dataIdent.add(MAX_DOWN_REGULATION_DURATION);
        dataIdent.add(REGULATION_RATE);
        dataIdent.add(UP_REGULATION_RATE);
        dataIdent.add(DOWN_REGULATION_RATE);
        dataIdent.add(REGULATION_PRECISION);
        dataIdent.add(CONTROL_ACCURACY);
        dataIdent.add(RAMP_SPEED);
        dataIdent.add(UP_RAMP_SPEED);
        dataIdent.add(DOWN_RAMP_SPEED);
        dataIdent.add(RAMP_DURATION);
        dataIdent.add(UP_RAMP_DURATION);
        dataIdent.add(DOWN_RAMP_DURATION);
        dataIdent.add(VPP_CODE);
        dataIdent.add(VPP_NAME_CN);
        dataIdent.add(TRADING_VARIETIES);
        dataIdent.add(RESOURCE_TYPES);
        dataIdent.add(CONTROL_START_TIME);
        dataIdent.add(CONTROL_END_TIME);
        dataIdent.add(TARGET_CONTROL_LOAD);

        // 用户编号信息类数据标识
        dataIdent.add(USER_INFO_01_20);
        dataIdent.add(USER_INFO_21_40);
        dataIdent.add(USER_INFO_41_60);
        dataIdent.add(USER_INFO_61_80);
        dataIdent.add(USER_INFO_81_100);

        // 用户功率信息类数据标识
        dataIdent.add(USER_POWER_01_20);
        dataIdent.add(USER_POWER_21_40);
        dataIdent.add(USER_POWER_41_60);
        dataIdent.add(USER_POWER_61_80);
        dataIdent.add(USER_POWER_81_100);

        // 基本信息类标识名称映射
        identifyname.put(Arrays.toString(AGGREGATE_USER_NUMBER), "aggregate_user_number");
        identifyname.put(Arrays.toString(AGGREGATE_CAPACITY), "aggregate_capacity");
        identifyname.put(Arrays.toString(REGULATION_CAPACITY), "regulation_capacity");
        identifyname.put(Arrays.toString(MAX_UP_REGULATION_CAPACITY), "max_up_regulation_capacity");
        identifyname.put(Arrays.toString(MAX_DOWN_REGULATION_CAPACITY), "max_down_regulation_capacity");
        identifyname.put(Arrays.toString(REGULATION_DURATION), "regulation_duration");
        identifyname.put(Arrays.toString(MAX_UP_REGULATION_DURATION), "max_up_regulation_duration");
        identifyname.put(Arrays.toString(MAX_DOWN_REGULATION_DURATION), "max_down_regulation_duration");
        identifyname.put(Arrays.toString(REGULATION_RATE), "regulation_rate");
        identifyname.put(Arrays.toString(UP_REGULATION_RATE), "up_regulation_rate");
        identifyname.put(Arrays.toString(DOWN_REGULATION_RATE), "down_regulation_rate");
        identifyname.put(Arrays.toString(REGULATION_PRECISION), "regulation_precision");
        identifyname.put(Arrays.toString(CONTROL_ACCURACY), "control_accuracy");
        identifyname.put(Arrays.toString(RAMP_SPEED), "ramp_speed");
        identifyname.put(Arrays.toString(UP_RAMP_SPEED), "up_ramp_speed");
        identifyname.put(Arrays.toString(DOWN_RAMP_SPEED), "down_ramp_speed");
        identifyname.put(Arrays.toString(RAMP_DURATION), "ramp_duration");
        identifyname.put(Arrays.toString(UP_RAMP_DURATION), "up_ramp_duration");
        identifyname.put(Arrays.toString(DOWN_RAMP_DURATION), "down_ramp_duration");
        identifyname.put(Arrays.toString(VPP_CODE), "vpp_code");
        identifyname.put(Arrays.toString(VPP_NAME_CN), "vpp_name_cn");
        identifyname.put(Arrays.toString(TRADING_VARIETIES), "trading_varieties");
        identifyname.put(Arrays.toString(RESOURCE_TYPES), "resource_types");
        identifyname.put(Arrays.toString(CONTROL_START_TIME), "control_start_time");
        identifyname.put(Arrays.toString(CONTROL_END_TIME), "control_end_time");
        identifyname.put(Arrays.toString(TARGET_CONTROL_LOAD), "target_control_load");

        // 用户编号信息类标识名称映射
        identifyname.put(Arrays.toString(USER_INFO_01_20), "user_info_01_20");
        identifyname.put(Arrays.toString(USER_INFO_21_40), "user_info_21_40");
        identifyname.put(Arrays.toString(USER_INFO_41_60), "user_info_41_60");
        identifyname.put(Arrays.toString(USER_INFO_61_80), "user_info_61_80");
        identifyname.put(Arrays.toString(USER_INFO_81_100), "user_info_81_100");

        // 用户功率信息类标识名称映射
        identifyname.put(Arrays.toString(USER_POWER_01_20), "user_power_01_20");
        identifyname.put(Arrays.toString(USER_POWER_21_40), "user_power_21_40");
        identifyname.put(Arrays.toString(USER_POWER_41_60), "user_power_41_60");
        identifyname.put(Arrays.toString(USER_POWER_61_80), "user_power_61_80");
        identifyname.put(Arrays.toString(USER_POWER_81_100), "user_power_81_100");

        // 基本信息类字节数映射（根据文档中的字节数）
        doc.put(Arrays.toString(AGGREGATE_USER_NUMBER), 3); // 3字节
        doc.put(Arrays.toString(AGGREGATE_CAPACITY), 11); // 11字节
        doc.put(Arrays.toString(REGULATION_CAPACITY), 11); // 11字节
        doc.put(Arrays.toString(MAX_UP_REGULATION_CAPACITY), 11); // 11字节
        doc.put(Arrays.toString(MAX_DOWN_REGULATION_CAPACITY), 11); // 11字节
        doc.put(Arrays.toString(REGULATION_DURATION), 3); // 3字节
        doc.put(Arrays.toString(MAX_UP_REGULATION_DURATION), 3); // 3字节
        doc.put(Arrays.toString(MAX_DOWN_REGULATION_DURATION), 3); // 3字节
        doc.put(Arrays.toString(REGULATION_RATE), 11); // 11字节
        doc.put(Arrays.toString(UP_REGULATION_RATE), 11); // 11字节
        doc.put(Arrays.toString(DOWN_REGULATION_RATE), 11); // 11字节
        doc.put(Arrays.toString(REGULATION_PRECISION), 11); // 11字节
        doc.put(Arrays.toString(CONTROL_ACCURACY), 11); // 11字节
        doc.put(Arrays.toString(RAMP_SPEED), 11); // 11字节
        doc.put(Arrays.toString(UP_RAMP_SPEED), 11); // 11字节
        doc.put(Arrays.toString(DOWN_RAMP_SPEED), 11); // 11字节
        doc.put(Arrays.toString(RAMP_DURATION), 3); // 3字节
        doc.put(Arrays.toString(UP_RAMP_DURATION), 3); // 3字节
        doc.put(Arrays.toString(DOWN_RAMP_DURATION), 3); // 3字节
        doc.put(Arrays.toString(VPP_CODE), 32); // 32字节
        doc.put(Arrays.toString(VPP_NAME_CN), 64); // 64字节
        doc.put(Arrays.toString(TRADING_VARIETIES), 8); // 8字节
        doc.put(Arrays.toString(RESOURCE_TYPES), 8); // 8字节
        doc.put(Arrays.toString(CONTROL_START_TIME), 128); // 128字节
        doc.put(Arrays.toString(CONTROL_END_TIME), 128); // 128字节
        doc.put(Arrays.toString(TARGET_CONTROL_LOAD), 128); // 128字节

        // 用户编号信息类字节数映射（可变长度，每个用户编号8字节，最多20个）
        doc.put(Arrays.toString(USER_INFO_01_20), 160); // 8*20=160字节
        doc.put(Arrays.toString(USER_INFO_21_40), 160); // 8*20=160字节
        doc.put(Arrays.toString(USER_INFO_41_60), 160); // 8*20=160字节
        doc.put(Arrays.toString(USER_INFO_61_80), 160); // 8*20=160字节
        doc.put(Arrays.toString(USER_INFO_81_100), 160); // 8*20=160字节

        // 用户功率信息类字节数映射（可变长度，每个用户功率11字节，最多20个）
        doc.put(Arrays.toString(USER_POWER_01_20), 220); // 11*20=220字节
        doc.put(Arrays.toString(USER_POWER_21_40), 220); // 11*20=220字节
        doc.put(Arrays.toString(USER_POWER_41_60), 220); // 11*20=220字节
        doc.put(Arrays.toString(USER_POWER_61_80), 220); // 11*20=220字节
        doc.put(Arrays.toString(USER_POWER_81_100), 220); // 11*20=220字节
    }

    public int getLength() {
        return identifyname.size();
    }

}
