package com.zjwly.dlt645.dataidentify;

import java.util.*;

/**
 * 广东
 *
 * <AUTHOR>
 * @date 2025/08/04 11:38
 */
public class DataIdentifyGD {
    public List<byte[]> dataIdent = new ArrayList<>();
    public Map<String, String> identifyname = new HashMap<String, String>();
    public Map<String, Integer> doc = new HashMap<String, Integer>();
    public int length;

    public DataIdentifyGD() {
        // 聚合用户数量（户）
        dataIdent.add(0, new byte[]{0x00, 0x01, 0x25, 0x02});

        identifyname.put(Arrays.toString(new byte[]{0x00, 0x01, 0x25, 0x02}), "aggregate_user_number");

        doc.put(Arrays.toString(new byte[]{0x00, 0x01, 0x25, 0x02}), 0);
    }

    public int getLength() {
        return identifyname.size();
    }

}
