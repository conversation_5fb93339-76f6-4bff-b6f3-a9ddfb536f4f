## 负荷类虚拟电厂运营商技术支持系统接入新型电力负荷管理系统交互字段示例

| 数据标识DI3 | 数据标识DI2 | 数据标识DI1 | 数据标识DI0 | 数据格式                | 字节数 | 单位   | 功能读 | 功能写 | 数据项名称                  |
| -------- | -------- | -------- | -------- | ----------------------- | ------ | ------ | ---- | ---- | --------------------------- |
| 02       | 25       | 01       | 00       | NNNNNN                  | 3      |        | *    |      | 聚t合用户数量（户）         |
| 02       | 25       | 01       | 01       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW     | *    |      | 聚合容量（kW）              |
| 02       | 25       | 01       | 02       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW     | *    | *    | 调节容量(kW)                |
| 02       | 25       | 01       | 03       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW     | *    | *    | 最大上调容量(kW)            |
| 02       | 25       | 01       | 04       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW     | *    | *    | 最大下调容量(kW）           |
| 02       | 25       | 01       | 05       | NNNNNN                  | 3      | min    | *    | *    | 调节持续时间(min)           |
| 02       | 25       | 01       | 06       | NNNNNN                  | 3      | min    | *    | *    | 最大上调节容量持续时间(min) |
| 02       | 25       | 01       | 07       | NNNNNN                  | 3      | min    | *    | *    | 最大下调节容量持续时间(min) |
| 02       | 25       | 01       | 08       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW/min | *    |      | 调节速率                    |
| 02       | 25       | 01       | 09       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW/min | *    |      | 上调速率                    |
| 02       | 25       | 01       | 0a       | NNNNNNNNNNNNNNNN.NNNNNN | 11     | kW/min | *    |      | 下调速率                    |
| 02   | 25   | 01   | 0b   | NNNNNNNNNNNNNNNN.NNNNNN | 11   | %      | *    |      | 调节精度                                                     |
| 02   | 25   | 01   | 0c   | NNNNNNNNNNNNNNNN.NNNNNN | 11   | %      | *    |      | 控制准确度                                                   |
| 02   | 25   | 01   | 0d   | NNNNNNNNNNNNNNNN.NNNNNN | 11   | kW/min | *    |      | 爬坡速度                                                     |
| 02   | 25   | 01   | 0e   | NNNNNNNNNNNNNNNN.NNNNNN | 11   | kW/min | *    |      | 上调爬坡速度                                                 |
| 02   | 25   | 01   | 0f   | NNNNNNNNNNNNNNNN.NNNNNN | 11   | kW/min | *    |      | 下调爬坡速度                                                 |
| 02   | 25   | 01   | 12   | NNNNNN                  | 3    | min    | *    |      | 爬坡时长                                                     |
| 02   | 25   | 01   | 13   | NNNNNN                  | 3    | min    | *    |      | 上调爬坡时长                                                 |
| 02   | 25   | 01   | 14   | NNNNNN                  | 3    | min    | *    |      | 下调爬坡时长                                                 |
| 02   | 25   | 01   | 15   | ASCII                   | 32   |        | *    |      | 虚拟电厂编号                                                 |
| 02   | 25   | 01   | 16   | ASCII                   | 64   |        | *    |      | 虚拟电厂中文                                                 |
| 02   | 25   | 01   | 17   | ASCII                   | 8    |        | *    |      | 参与交易品种  当有多个时，使用英文逗号分隔:01电能量市场，02需求响应市场，03  辅助服务市场 |
| 02   | 25   | 01   | 18   | ASCII                   | 8    |        | *    |      | 聚合资源类型  当有多个时，使用英文逗号分隔:  01自备电源，02用户侧储能，03电动汽车，04充电站，05 换电站，06楼宇空调，07 工商业可调节负荷，08分布式光伏，09分散式风电，10分布式独立储能，99  其他 |
| 02   | 25   | 01   | 19   | Hhmmss                  | 128  |        | *    |      | 调控开始时间  格式为加密前格式，经sdk加密后，长度固定为128字节 |
| 02   | 25   | 01   | 1A   | Hhmmss                  | 128  |        | *    |      | 调控结束时间  格式为加密前格式，经sdk加密后，长度固定为128字节 |
| 02   | 25   | 01   | 1B   | NNNNNNNNNNNNNNNN.NNNNNN | 128  | kW     | *    |      | 目标调控负荷  格式为加密前格式，经sdk加密后，长度固定为128字节 |
| 02   | 25   | 02   | 01   | NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN, | 8,  ...  8,  |      | *    |      | 用户编号信息(N 最大值为20)  用户1用户编号,  用户2用户编号,  ...  用户20用户编号, |
| 02   | 25   | 02   | 02   | NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN, | 8,  ...  8,  |      | *    |      | 用户编号信息(N 最大值为20)  用户21用户编号,  用户22用户编号,  ...  用户40用户编号, |
| 02   | 25   | 02   | 03   | NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN, | 8,  ...  8,  |      | *    |      | 用户编号信息(N 最大值为20)  用户41用户编号,  用户42用户编号,  ...  用户60用户编号, |
| 02   | 25   | 02   | 04   | NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN, | 8,  ...  8,  |      | *    |      | 用户编号信息(N 最大值为20)  用户61用户编号,  用户62用户编号,  ...  用户80用户编号, |
| 02   | 25   | 02   | 05   | NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN,  NNNNNNNNNNNNNNNN, | 8,  ...  8,  |      | *    |      | 用户编号信息(N 最大值为20)  用户81用户编号,  用户82用户编号,  ...  用户100用户编号, |
| 02   | 25   | 03   | 01   | NNNNNNNNNNNNNNNN.NNNNNN  ...  NNNNNNNNNNNNNNNN.NNNNNN   | 11,  ...  11 | KW   | *    |      | 用户功率信息  用户1功率，  ...  用户20功率，                 |
| 02   | 25   | 03   | 02   | NNNNNNNNNNNNNNNN.NNNNNN  ...  NNNNNNNNNNNNNNNN.NNNNNN   | 11,  ...  11 | KW   | *    |      | 用户功率信息  用户21功率，  ...  用户40功率，                |
| 02   | 25   | 03   | 03   | NNNNNNNNNNNNNNNN.NNNNNN  ...  NNNNNNNNNNNNNNNN.NNNNNN   | 11,  ...  11 | KW   | *    |      | 用户功率信息  用户41功率，  ...  用户60功率，                |
| 02   | 25   | 03   | 04   | NNNNNNNNNNNNNNNN.NNNNNN  ...  NNNNNNNNNNNNNNNN.NNNNNN   | 11,  ...  11 | KW   | *    |      | 用户功率信息  用户61功率，  ...  用户80功率，                |
| 02   | 25   | 03   | 05   | NNNNNNNNNNNNNNNN.NNNNNN  ...  NNNNNNNNNNNNNNNN.NNNNNN   | 11,  ...  11 | KW   | *    |      | 用户功率信息  用户81功率，  ...  用户100功率，               |

注：用户编号信息和用户功率信息，为可变长度数据项，有多少个就返回多少个，不需要补无效数据，一个数据项最多返回20个数据，若数量大于20个，则通过后续数据标识读取。比如02250201返回前20个，02250202返回21-40个，02250203返回41-60个等

